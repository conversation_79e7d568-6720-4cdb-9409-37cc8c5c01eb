const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const { validateApiKey } = require("./services/aiService");

dotenv.config();

const app = express();

app.use(cors());
app.use(express.json());

Promise.all([
  mongoose.connect(
    process.env.MONGODB_URI || "mongodb://localhost:27017/mental-health-app"
  ),
  validateApiKey(),
])
  .then(([, isApiKeyValid]) => {
    console.log("MongoDB connected");
    if (!isApiKeyValid) {
      console.warn("OpenAI features will be disabled due to invalid API key");
    }
  })
  .catch((err) => {
    console.error("Startup error:", err);
  });

const authRoutes = require("./routes/auth");
const moodRoutes = require("./routes/mood");
const resourcesRoutes = require("./routes/resources");
const exercisesRoutes = require("./routes/exercises");
const journalRoutes = require("./routes/journal");
const goalsRoutes = require("./routes/goals");
const postsRoutes = require("./routes/posts");
const aiRoutes = require("./routes/ai");

app.use("/api/auth", authRoutes);
app.use("/api/mood", moodRoutes);
app.use("/api/resources", resourcesRoutes);
app.use("/api/exercises", exercisesRoutes);
app.use("/api/journal", journalRoutes);
app.use("/api/goals", goalsRoutes);
app.use("/api/posts", postsRoutes);
app.use("/api/ai", aiRoutes);

if (process.env.NODE_ENV === "production") {
  app.use(express.static(path.join(__dirname, "../client/build")));
  app.get("*", (_, res) => {
    res.sendFile(path.resolve(__dirname, "../client/build", "index.html"));
  });
}

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
