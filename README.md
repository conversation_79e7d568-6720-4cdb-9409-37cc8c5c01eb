# MindTrack - Mental Health Tracker

A personal project to help track mental wellness and build healthy habits.

## What it does

- Track daily mood and energy levels
- Journal your thoughts and experiences  
- Set and monitor wellness goals
- Browse mental health resources
- Connect with a supportive community

## Tech Stack

**Frontend:** React, Styled Components, Chart.js
**Backend:** Node.js, Express, MongoDB
**Other:** JWT auth, OpenAI integration (optional)

## Getting Started

1. Clone the repo
2. Install dependencies:
   ```bash
   cd server && npm install
   cd ../client && npm install
   ```
3. Set up your environment:
   - Copy `server/.env.example` to `server/.env`
   - Add your MongoDB URI and other config
4. Start the development servers:
   ```bash
   # Terminal 1 - Backend
   cd server && npm run dev
   
   # Terminal 2 - Frontend  
   cd client && npm run dev
   ```

## Features

- **Mood Tracking**: Log daily mood, energy, sleep, and activities
- **Journaling**: Write and reflect on your experiences
- **Goal Setting**: Create and track wellness goals
- **Resources**: Browse helpful mental health resources
- **Community**: Share experiences and support others
- **AI Chat**: Optional AI assistant for guidance (requires OpenAI API key)

## Note

This is a personal project for learning and self-tracking. It's not intended to replace professional mental health care. If you're struggling, please reach out to a qualified professional.

## Contributing

This is mainly a personal project, but feel free to fork it and make it your own!

---

Built with ❤️ by Abhay Singh Tomar
